# Chinese Travelogue Narrative Analysis - Implementation Plan


## 🎯 Project Overview

This document outlines the detailed implementation plan for adapting the word embedding measures project to analyze Chinese travelogues using CLIP embeddings instead of FastText. The goal is to process 6000 Chinese travelogues (~4000 characters each) and extract narrative features (speed, volume, circuitousness) while maintaining full backward compatibility.

## 📋 Requirements Summary

- **Dataset**: 6000 Chinese travelogues (4000 characters each)
- **Chunking**: Sentence-based segmentation targeting ~100 chunks per travelogue
- **Embeddings**: CLIP multilingual model (512-dimensional)
- **Features**: Preserve existing narrative analysis (speed, volume, circuitousness)

## 🔍 Key Questions & Answers

### Q1: Travelogue Dataset Content Requirements
**Answer**: Only text content is required. The system maps it to the existing `ABSTRACT` field.

**Recommended Structure**:
```json
[
    {
        "travelId": "7687900",
        "source": "草原观赏季，专程从北京赶到内蒙古乌兰布统大草原北部的汗苏鲁国际生态牧场，远离城镇，自驾是最佳旅行方式，虽然路途遥远，但绝对不虚此行。\n“汗苏鲁”蒙古语是“皇家”的意思，清朝时期这里是蒙古王爷向朝廷纳贡牛羊育肥的牧场，曾经的皇家牧场，现在是迷人的草原观赏区。\n和传统的牧场不同，有多种住宿方式，现代化的蒙古包内部非常宽敞，独立卫浴，干湿分离，双窗方便通风，采光也非常好。\n安静的坐在蒙古包门口，望着蓝天白云下的蒙古包，发呆也竟然如此美好。此时的汗苏鲁国际生态牧场，清晨4点多天就亮了，看个草原日出再睡个回笼觉也完全可以。\n五颜六色的馒头房，造型可爱，在牧场里更像是住进童话故事一样，这也是汗苏鲁国际生态牧场最受欢迎的住宿方式之一。\n另外，住宿区还有很多简约风的各种木屋，非常适合家庭度假。"
    },
    ...
    ...
]
```

### Q2: Chinese Punctuation for Segmentation
**Enhanced Punctuation Set**:
- **Primary**: `。！？；` + paragraph breaks (`\n`)
- **Secondary**: `：…` for fine-tuning chunk balance
- **Regex**: `r'[。！？；.!?;]|\n\s*'` (primary) + `r'[：…]'` (secondary)

### Q3: CLIP Model Selection
**Chosen**: `openai/clip-vit-base-patch32`

**Rationale**:
- ✅ Text-focused semantic understanding
- ✅ Optimized for narrative analysis tasks

use clip extract the embedding of text, you should L2 normalization

## 🏗️ Detailed Task Breakdown

### Task 1: Abstract Embedding Interface Design
**Priority**: Critical Foundation
**Estimated Time**: 2-3 days

**Objective**: Create unified interface supporting CLIP embeddings with 512 dimension.

**Implementation Details**:
```python
# utils/embeddings.py - New Abstract Interface
class EmbeddingModel(ABC):
    @abstractmethod
    def get_embeddings(self, chunks: List[str]) -> List[np.ndarray]:
        pass


class CLIPEmbedding(EmbeddingModel):
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32"):
        from sentence_transformers import SentenceTransformer
        self.model = SentenceTransformer(model_name)
    
    def get_embeddings(self, chunks: List[str]) -> List[np.ndarray]:
        # Batch process chunks for efficiency
        return self.model.encode(chunks)
```

**Key Changes**:
1. Modify `get_chunk_embeddings()` to use fixed 512 dimensions
2. Add factory function `create_embedding_model()`

**Verification Criteria**:
- [ ] CLIP embeddings generate correct 512-dim vectors
- [ ] Existing code paths remain functional

### Task 2: Chinese Text Processor Implementation
**Priority**: High
**Estimated Time**: 2-3 days

**Objective**: Implement Chinese sentence segmentation and balanced chunking strategy.

**Implementation Details**:
```python
# utils/data.py - Text Processing Abstraction
class TextProcessor(ABC):
    @abstractmethod
    def get_chunks(self, document: str, target_chunks: int) -> List[str]:
        pass

class ChineseProcessor(TextProcessor):
    def __init__(self):
        # Primary segmentation pattern
        self.primary_pattern = re.compile(r'[。！？；.!?;]|\n\s*')
        # Secondary for fine-tuning
        self.secondary_pattern = re.compile(r'[：…]')
    
    def get_chunks(self, document: str, target_chunks: int = 100) -> List[str]:
        # 1. Split by primary punctuation
        sentences = self.primary_pattern.split(document)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # 2. Group sentences to reach target chunk count
        if len(sentences) > target_chunks:
            # Merge sentences
            chunks = self._merge_sentences(sentences, target_chunks)
        elif len(sentences) < target_chunks * 0.8:
            # Further split using secondary punctuation
            chunks = self._split_further(sentences, target_chunks)
        else:
            chunks = sentences
        
        return chunks[:target_chunks]  # Ensure exact count
```

**Chunking Strategy**:
1. **Primary Split**: Use `[。！？；.!?;]` and paragraph breaks
2. **Balance Check**: Target 100±10 chunks TODO: I don't think it is necessary to be so strict? why need to get exactly 100 chunks?
3. **Merge Strategy**: Combine short sentences if too many chunks
4. **Split Strategy**: Use secondary punctuation if too few chunks
5. **Length Balance**: Aim for relatively uniform chunk lengths

**Verification Criteria**:
- [ ] Chinese sentences correctly segmented
- [ ] Chunk count within target range (90-110)
- [ ] Reasonable length distribution
- [ ] Handles edge cases (very short/long texts)

### Task 3: CLIP Model Integration & Configuration
**Priority**: High  
**Estimated Time**: 2-3 days

**Objective**: Integrate CLIP model with command-line configuration support.

**Dependencies Installation**:

**Command-Line Extensions**:
```python
# main.py - Extended Arguments
parser.add_argument("--embedding_type", choices=['clip'], 
                   default='clip', help="Embedding model type")
parser.add_argument("--language", choices=['en', 'zh'], 
                   default='zh', help="Text language")
parser.add_argument("--clip_model", type=str, 
                   default='openai/clip-vit-base-patch32',
                   help="CLIP model name")
parser.add_argument("--embedding_dim", type=int, 
                   help="Override embedding dimension")
```

**Model Loading Logic**:
```python
def create_embedding_model(args) -> EmbeddingModel:
    if args.embedding_type == 'clip':
        return CLIPEmbedding(args.clip_model)
    else:
        return FastTextEmbedding(args.proj_dir + args.model_name)
```

**Verification Criteria**:
- [ ] CLIP model loads successfully
- [ ] Chinese text embeddings generated correctly
- [ ] Batch processing efficient for large datasets
- [ ] Command-line parameters work as expected
- [ ] Error handling for model loading failures

### Task 4: Chinese Travelogue Data Loader
**Priority**: Medium
**Estimated Time**: 1-2 days

**Objective**: Implement data loading for Chinese travelogue formats.


**Verification Criteria**:
- [ ] Successfully loads all travelogues in data.json
- [ ] Handles both JSON formats
- [ ] Memory usage reasonable for large datasets
- [ ] Control variables extracted correctly
- [ ] Error handling for malformed data

### Task 5: Integration Testing & Validation
**Priority**: Critical
**Estimated Time**: 2-3 days

**Objective**: Comprehensive testing and validation of the complete pipeline.

**Test Script Structure**:
```python
# test_chinese_clip.py - Comprehensive Testing
def test_small_sample():
    """Test with small sample of Chinese travelogues"""
    sample_texts = [
        "今天我们来到了北京故宫。这里的建筑真是令人叹为观止！每一个细节都体现了古代工匠的智慧。",
        "上海的夜景非常美丽。外滩的灯光倒映在黄浦江中，形成了一幅动人的画面。我们在这里拍了很多照片。"
    ]
    
    # Test chunking
    processor = ChineseProcessor()
    chunks = [processor.get_chunks(text, 10) for text in sample_texts]
    assert all(8 <= len(chunk_list) <= 12 for chunk_list in chunks)
    
    # Test embeddings
    model = CLIPEmbedding()
    embeddings = [model.get_embeddings(chunk_list) for chunk_list in chunks]
    assert all(emb.shape[1] == 512 for emb_list in embeddings for emb in emb_list)
    
    # Test features
    features = [get_features(emb_list) for emb_list in embeddings]
    assert all('speed' in f and 'volume' in f and 'circuitousness' in f for f in features)

def test_full_pipeline():
    """Test complete pipeline with sample data"""
    # Create test configuration
    args = create_test_args()
    args.embedding_type = 'clip'
    args.language = 'zh'
    args.T = 100
    
    # Run pipeline
    model = create_embedding_model(args)
    data = load_chinese_travelogues(args)
    chunk_embs = setup_chunk_embeddings(args, model, get_data_property(data, ABSTRACT))
    features = [get_features(chunk_emb) for chunk_emb in chunk_embs]
    
    # Validate results
    assert len(features) == len(data)
    assert all(isinstance(f['speed'], float) for f in features)
    assert all(isinstance(f['volume'], float) for f in features)

def benchmark_performance():
    """Compare FastText vs CLIP performance"""
    # Test same data with both models
    # Record processing time and memory usage
    # Compare feature distributions
```

**Backward Compatibility Tests**:
```python
def test_backward_compatibility():
    """Ensure existing FastText workflow unchanged"""
    # Test original commands still work
    # Test existing chunk_embs.txt files load correctly
    # Test feature extraction produces same results
```

**Documentation Updates**:
```markdown
# README.md - Usage Examples

## Chinese Travelogue Analysis

### First Run (Generate Embeddings)
```bash
python main.py --embedding_type clip --language zh --data_file data/data.json --T 100 --limit 6000
```

### Subsequent Runs (Load Existing Embeddings)
```bash
python main.py --embedding_type clip --language zh --chunk_embs_file data/chinese_chunk_embs.txt --T 100
```

### Mixed Usage (CLIP for Chinese)
```bash
# Chinese travelogues (new)
python main.py --embedding_type clip --language zh --data_file data/data.json
```
```

**Verification Criteria**:
- [ ] End-to-end pipeline executes without errors
- [ ] Generated features are reasonable and interpretable
- [ ] Performance acceptable for all travelogues in data.json
- [ ] Backward compatibility 100% maintained
- [ ] Documentation clear and comprehensive
- [ ] Test coverage >80%

## 🚀 Implementation Timeline

| Week | Tasks | Deliverables |
|------|-------|-------------|
| Week 1 | Task 1 + Task 2 | Abstract interfaces + Chinese text processing |
| Week 2 | Task 3 + Task 4 | CLIP integration + Dynamic dimensions |
| Week 3 | Task 5 + Task 6 | Data loading + Testing & validation |

## 📊 Expected Outcomes

1. **Functionality**: Complete Chinese travelogue narrative analysis capability
3. **Quality**: Meaningful narrative features for Chinese text
4. **Compatibility**: Zero impact on existing FastText workflows
5. **Extensibility**: Framework for future language/model additions

